/* Header Styles */
.header {
  background-color: #CDFF9A;
  padding: 15px 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 100;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #000000;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.logo-text {
  display: flex;
  flex-direction: column;
  line-height: 1;
}

.nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}

.nav-link {
  text-decoration: none;
  color: #000000;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #ffffff;
}

.catalog-trigger {
  cursor: pointer;
  user-select: none;
}

.icons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.dropdown-menu {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  background-color: #ffffff;
  width: 100vw;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 99;
  padding: 40px 50px;
}

.dropdown-menu.hidden {
  display: none;
}

.dropdown-content {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  gap: 60px;
}

.category-column {
  width: 200px;
}

.category-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.category-items {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 30px;
  flex: 1;
}

.category-item {
  text-align: center;
  text-decoration: none;
  color: #333;
  transition: transform 0.3s ease;
}

.category-image {
  width: 150px;
  height: 150px;
  border-radius: 8px;
  margin-bottom: 15px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-image:hover {
  transform: scale(1.05);
}

.category-label {
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #333;
}
